#!/usr/bin/env python3
"""
Simple test script for the ecommerce scraping agents.
This script tests the core Stagehand functionality without requiring full CrewAI setup.
"""

import os
import json
import time
import asyncio
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.table import Table

# Load environment variables
load_dotenv()

console = Console()

def check_environment() -> bool:
    """Check if required environment variables are set."""
    required_vars = {
        "BROWSERBASE_API_KEY": "Browserbase API key for browser automation",
        "BROWSERBASE_PROJECT_ID": "Browserbase project ID",
    }
    
    # Check for LLM API key
    llm_keys = {
        "OPENAI_API_KEY": "OpenAI API key for GPT models",
        "ANTHROPIC_API_KEY": "Anthropic API key for Claude models"
    }
    
    missing_vars = []
    
    # Check required vars
    for var, description in required_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"❌ {var}: {description}")
    
    # Check LLM keys (at least one required)
    has_llm_key = any(os.getenv(key) for key in llm_keys.keys())
    if not has_llm_key:
        missing_vars.append("❌ LLM API Key: Need either OPENAI_API_KEY or ANTHROPIC_API_KEY")
    
    if missing_vars:
        console.print(Panel("\n".join(missing_vars), title="❌ Missing Environment Variables", border_style="red"))
        console.print("\n💡 Create a .env file with these variables or set them in your environment.")
        return False
    
    console.print(Panel("✅ All required environment variables are set!", title="Environment Check", border_style="green"))
    return True

async def test_stagehand_basic() -> Dict[str, Any]:
    """Test basic Stagehand functionality."""
    console.print("\n🧪 Testing Basic Stagehand Functionality")
    console.print("=" * 50)

    try:
        from stagehand import Stagehand
        from stagehand.schemas import AvailableModel

        # Determine which model to use
        if os.getenv("OPENAI_API_KEY"):
            model_name = AvailableModel.GPT_4O
            model_api_key = os.getenv("OPENAI_API_KEY")
        else:
            model_name = AvailableModel.CLAUDE_3_5_SONNET
            model_api_key = os.getenv("ANTHROPIC_API_KEY")

        console.print(f"🤖 Using model: {model_name.value}")

        # Initialize Stagehand
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
            transient=True
        ) as progress:
            task = progress.add_task("Initializing Stagehand...", total=None)

            stagehand = Stagehand(
                api_key=os.getenv("BROWSERBASE_API_KEY"),
                project_id=os.getenv("BROWSERBASE_PROJECT_ID"),
                model_api_key=model_api_key,
                model_name=model_name,
                headless=True,
                verbose=1
            )

            progress.update(task, description="Stagehand initialized ✅")
            await asyncio.sleep(1)

        return {
            "success": True,
            "message": "Stagehand initialized successfully",
            "model": model_name.value
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to initialize Stagehand"
        }

def test_simple_navigation() -> Dict[str, Any]:
    """Test simple navigation using CrewAI StagehandTool."""
    console.print("\n🌐 Testing Website Navigation")
    console.print("=" * 50)

    try:
        from crewai_tools import StagehandTool
        from stagehand.schemas import AvailableModel

        # Determine which model to use
        if os.getenv("OPENAI_API_KEY"):
            model_name = AvailableModel.GPT_4O
            model_api_key = os.getenv("OPENAI_API_KEY")
        else:
            model_name = AvailableModel.CLAUDE_3_5_SONNET
            model_api_key = os.getenv("ANTHROPIC_API_KEY")

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
            transient=True
        ) as progress:
            task = progress.add_task("Setting up StagehandTool...", total=None)

            # Initialize CrewAI StagehandTool
            stagehand_tool = StagehandTool(
                api_key=os.getenv("BROWSERBASE_API_KEY"),
                project_id=os.getenv("BROWSERBASE_PROJECT_ID"),
                model_api_key=model_api_key,
                model_name=model_name,
                headless=True,
                verbose=1
            )

            progress.update(task, description="Navigating to test site...")

            # Navigate to a simple test site and extract data
            test_url = "https://httpbin.org/html"
            result = stagehand_tool.run(
                instruction="Extract the page title and any visible text content from this HTML page",
                url=test_url,
                command_type="extract"
            )

            progress.update(task, description="Navigation test completed ✅")
            time.sleep(1)

        return {
            "success": True,
            "url": test_url,
            "page_info": result,
            "message": "Successfully navigated and extracted data using CrewAI StagehandTool"
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to navigate or extract data"
        }

def test_ecommerce_simulation() -> Dict[str, Any]:
    """Test ecommerce data extraction using CrewAI StagehandTool."""
    console.print("\n🛒 Testing Ecommerce Data Extraction")
    console.print("=" * 50)

    try:
        from crewai_tools import StagehandTool
        from stagehand.schemas import AvailableModel

        # Determine which model to use
        if os.getenv("OPENAI_API_KEY"):
            model_name = AvailableModel.GPT_4O
            model_api_key = os.getenv("OPENAI_API_KEY")
        else:
            model_name = AvailableModel.CLAUDE_3_5_SONNET
            model_api_key = os.getenv("ANTHROPIC_API_KEY")

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
            transient=True
        ) as progress:
            task = progress.add_task("Setting up StagehandTool...", total=None)

            # Initialize CrewAI StagehandTool
            stagehand_tool = StagehandTool(
                api_key=os.getenv("BROWSERBASE_API_KEY"),
                project_id=os.getenv("BROWSERBASE_PROJECT_ID"),
                model_api_key=model_api_key,
                model_name=model_name,
                headless=True,
                verbose=1
            )

            progress.update(task, description="Navigating to demo ecommerce site...")

            # Use a demo ecommerce site for testing
            test_url = "https://demo.opencart.com/"

            progress.update(task, description="Extracting product information...")

            # Extract product information
            product_info = stagehand_tool.run(
                instruction="""Extract information about products visible on this page including:
                - Product names/titles
                - Prices if visible
                - Any product descriptions
                - Product images or links
                Return the data in a structured JSON format.""",
                url=test_url,
                command_type="extract"
            )

            progress.update(task, description="Ecommerce test completed ✅")
            time.sleep(1)

        return {
            "success": True,
            "url": test_url,
            "product_info": product_info,
            "message": "Successfully extracted ecommerce data using CrewAI StagehandTool"
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to extract ecommerce data"
        }

def display_results(results: Dict[str, Any]) -> None:
    """Display test results in a formatted table."""
    table = Table(title="🧪 Agent Test Results")
    table.add_column("Test", style="cyan", no_wrap=True)
    table.add_column("Status", style="magenta")
    table.add_column("Details", style="green")
    
    for test_name, result in results.items():
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        details = result.get("message", "No details")
        if not result["success"] and "error" in result:
            details += f"\nError: {result['error']}"
        
        table.add_row(test_name, status, details)
    
    console.print(table)

def save_results(results: Dict[str, Any]) -> None:
    """Save test results to a JSON file."""
    filename = f"agent_test_results_{int(time.time())}.json"
    with open(filename, "w") as f:
        json.dump(results, f, indent=2, default=str)
    console.print(f"\n💾 Results saved to: {filename}")

async def main():
    """Run all agent tests."""
    console.print(Panel("🚀 Ecommerce Scraping Agent Tests", style="bold blue"))

    # Check environment
    if not check_environment():
        return

    # Run tests
    results = {}

    console.print("\n" + "="*60)

    # Test 1: Basic Stagehand functionality
    results["Basic Stagehand"] = await test_stagehand_basic()

    # Test 2: Simple navigation
    if results["Basic Stagehand"]["success"]:
        results["Website Navigation"] = test_simple_navigation()
    else:
        results["Website Navigation"] = {
            "success": False,
            "message": "Skipped due to basic Stagehand failure"
        }

    # Test 3: Ecommerce simulation
    if results["Website Navigation"]["success"]:
        results["Ecommerce Extraction"] = test_ecommerce_simulation()
    else:
        results["Ecommerce Extraction"] = {
            "success": False,
            "message": "Skipped due to navigation failure"
        }

    # Display results
    console.print("\n" + "="*60)
    display_results(results)

    # Save results
    save_results(results)

    # Summary
    passed = sum(1 for r in results.values() if r["success"])
    total = len(results)

    if passed == total:
        console.print(Panel(f"🎉 All tests passed! ({passed}/{total})", style="bold green"))
    else:
        console.print(Panel(f"⚠️  {passed}/{total} tests passed", style="bold yellow"))

    console.print("\n💡 Next steps:")
    console.print("  1. If tests pass, try running the full examples in the 'examples/' folder")
    console.print("  2. Check the saved results file for detailed information")
    console.print("  3. Set up additional environment variables for full CrewAI functionality")

async def async_main():
    """Async wrapper for main function."""
    await main()

if __name__ == "__main__":
    try:
        asyncio.run(async_main())
    except KeyboardInterrupt:
        console.print("\n⏹️  Tests interrupted by user")
    except Exception as e:
        console.print(f"\n❌ Unexpected error: {str(e)}")
